"""
Historical Data Simulator for NewQuantAgent Backtesting

Simulates realistic memecoin market data for backtesting purposes.
Generates price movements, volume patterns, and market events based on real memecoin characteristics.
"""

import asyncio
import random
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from decimal import Decimal

logger = logging.getLogger(__name__)

@dataclass
class MarketDataPoint:
    """Single market data point for backtesting"""
    timestamp: float
    token_address: str
    price: float
    volume_24h: float
    market_cap: float
    holder_count: int
    bonding_curve_progress: float
    liquidity_usd: float
    price_change_1h: float
    price_change_24h: float
    is_graduated: bool = False
    social_mentions: int = 0
    dev_activity: float = 0.0

class HistoricalDataSimulator:
    """
    Simulates realistic memecoin market data for backtesting.
    
    Features:
    - Realistic price movements with volatility clustering
    - Volume patterns correlated with price movements
    - Bonding curve progression simulation
    - Market events (pumps, dumps, graduations)
    - Social sentiment simulation
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Simulation parameters
        self.BASE_VOLATILITY = 0.15  # 15% base daily volatility
        self.VOLUME_CORRELATION = 0.7  # Price-volume correlation
        self.GRADUATION_THRESHOLD = 69000  # Pump.fun graduation threshold
        self.MIN_HOLDERS = 10
        self.MAX_HOLDERS = 5000
        
        # Market event probabilities
        self.PUMP_PROBABILITY = 0.05  # 5% chance per hour
        self.DUMP_PROBABILITY = 0.03  # 3% chance per hour
        self.GRADUATION_PROBABILITY = 0.01  # 1% chance per hour if near threshold
        
        logger.info("Historical data simulator initialized")
    
    async def generate_historical_data(
        self,
        token_addresses: List[str],
        start_time: datetime,
        end_time: datetime,
        interval_minutes: int = 60
    ) -> Dict[str, List[MarketDataPoint]]:
        """
        Generate historical market data for multiple tokens.
        
        Args:
            token_addresses: List of token addresses to simulate
            start_time: Start of simulation period
            end_time: End of simulation period
            interval_minutes: Data point interval in minutes
            
        Returns:
            Dictionary mapping token addresses to historical data points
        """
        logger.info(f"Generating historical data for {len(token_addresses)} tokens")
        logger.info(f"Period: {start_time} to {end_time}")
        logger.info(f"Interval: {interval_minutes} minutes")
        
        historical_data = {}
        
        for token_address in token_addresses:
            logger.info(f"Simulating data for token {token_address}")
            
            # Generate token-specific characteristics
            token_profile = self._generate_token_profile(token_address)
            
            # Generate time series data
            data_points = await self._generate_token_timeseries(
                token_address,
                token_profile,
                start_time,
                end_time,
                interval_minutes
            )
            
            historical_data[token_address] = data_points
            logger.info(f"Generated {len(data_points)} data points for {token_address}")
        
        return historical_data
    
    def _generate_token_profile(self, token_address: str) -> Dict[str, Any]:
        """Generate realistic token characteristics"""
        # Use token address as seed for reproducible results
        random.seed(hash(token_address) % (2**32))
        
        profile = {
            'initial_price': random.uniform(0.000001, 0.01),  # $0.000001 - $0.01
            'base_volatility': random.uniform(0.1, 0.4),  # 10-40% daily volatility
            'volume_multiplier': random.uniform(0.5, 3.0),  # Volume variation
            'social_activity': random.uniform(0.1, 1.0),  # Social engagement level
            'dev_activity': random.uniform(0.0, 0.8),  # Developer activity
            'pump_tendency': random.uniform(0.0, 1.0),  # Likelihood of pumps
            'graduation_potential': random.uniform(0.1, 0.9),  # Graduation likelihood
            'holder_growth_rate': random.uniform(0.01, 0.1)  # Holder acquisition rate
        }
        
        return profile
    
    async def _generate_token_timeseries(
        self,
        token_address: str,
        profile: Dict[str, Any],
        start_time: datetime,
        end_time: datetime,
        interval_minutes: int
    ) -> List[MarketDataPoint]:
        """Generate time series data for a single token"""
        
        data_points = []
        current_time = start_time
        
        # Initialize state
        current_price = profile['initial_price']
        current_volume = random.uniform(1000, 50000)  # Initial volume
        current_holders = random.randint(self.MIN_HOLDERS, 100)
        bonding_progress = 0.0
        is_graduated = False
        
        while current_time <= end_time:
            # Calculate time-based factors
            hours_elapsed = len(data_points)
            
            # Generate price movement
            price_change = self._generate_price_movement(
                current_price, profile, hours_elapsed
            )
            current_price = max(0.000001, current_price * (1 + price_change))
            
            # Generate volume (correlated with price movement)
            volume_change = self._generate_volume_change(price_change, profile)
            current_volume = max(100, current_volume * (1 + volume_change))
            
            # Update holders (gradual growth with price correlation)
            if price_change > 0.1:  # 10% price increase
                holder_increase = random.randint(1, 10)
                current_holders = min(self.MAX_HOLDERS, current_holders + holder_increase)
            elif price_change < -0.2:  # 20% price decrease
                holder_decrease = random.randint(0, 3)
                current_holders = max(self.MIN_HOLDERS, current_holders - holder_decrease)
            
            # Update bonding curve progress
            if not is_graduated:
                market_cap = current_price * 1_000_000_000  # Assume 1B supply
                bonding_progress = min(1.0, market_cap / self.GRADUATION_THRESHOLD)
                
                # Check for graduation
                if (bonding_progress > 0.9 and 
                    random.random() < self.GRADUATION_PROBABILITY * profile['graduation_potential']):
                    is_graduated = True
                    bonding_progress = 1.0
                    logger.info(f"Token {token_address} graduated at {current_time}")
            
            # Calculate derived metrics
            market_cap = current_price * 1_000_000_000
            liquidity_usd = market_cap * random.uniform(0.05, 0.3)  # 5-30% of market cap
            
            # Price changes (simplified)
            price_change_1h = price_change
            price_change_24h = price_change * random.uniform(0.8, 1.2)
            
            # Social metrics
            social_mentions = int(current_holders * profile['social_activity'] * random.uniform(0.5, 2.0))
            
            # Create data point
            data_point = MarketDataPoint(
                timestamp=current_time.timestamp(),
                token_address=token_address,
                price=current_price,
                volume_24h=current_volume,
                market_cap=market_cap,
                holder_count=current_holders,
                bonding_curve_progress=bonding_progress,
                liquidity_usd=liquidity_usd,
                price_change_1h=price_change_1h,
                price_change_24h=price_change_24h,
                is_graduated=is_graduated,
                social_mentions=social_mentions,
                dev_activity=profile['dev_activity'] * random.uniform(0.8, 1.2)
            )
            
            data_points.append(data_point)
            current_time += timedelta(minutes=interval_minutes)
        
        return data_points
    
    def _generate_price_movement(
        self, 
        current_price: float, 
        profile: Dict[str, Any], 
        hours_elapsed: int
    ) -> float:
        """Generate realistic price movement"""
        
        # Base random walk
        base_change = np.random.normal(0, profile['base_volatility'] / 24)  # Hourly volatility
        
        # Add market events
        if random.random() < self.PUMP_PROBABILITY * profile['pump_tendency']:
            # Pump event
            pump_magnitude = random.uniform(0.2, 2.0)  # 20% to 200% pump
            base_change += pump_magnitude
            logger.debug(f"Pump event: +{pump_magnitude:.1%}")
        
        elif random.random() < self.DUMP_PROBABILITY:
            # Dump event
            dump_magnitude = random.uniform(-0.8, -0.3)  # 30% to 80% dump
            base_change += dump_magnitude
            logger.debug(f"Dump event: {dump_magnitude:.1%}")
        
        # Add volatility clustering (higher volatility after big moves)
        if abs(base_change) > 0.1:
            base_change *= random.uniform(1.0, 1.5)
        
        return base_change
    
    def _generate_volume_change(self, price_change: float, profile: Dict[str, Any]) -> float:
        """Generate volume change correlated with price movement"""
        
        # Volume increases with price volatility
        volatility_factor = abs(price_change) * 2
        
        # Base volume change
        base_volume_change = np.random.normal(0, 0.3)  # 30% volume volatility
        
        # Add correlation with price movement
        correlated_change = price_change * self.VOLUME_CORRELATION * profile['volume_multiplier']
        
        return base_volume_change + volatility_factor + correlated_change
    
    async def get_market_data_at_time(
        self,
        historical_data: Dict[str, List[MarketDataPoint]],
        timestamp: float
    ) -> Dict[str, MarketDataPoint]:
        """Get market data for all tokens at a specific timestamp"""
        
        market_snapshot = {}
        
        for token_address, data_points in historical_data.items():
            # Find closest data point to timestamp
            closest_point = min(
                data_points,
                key=lambda x: abs(x.timestamp - timestamp)
            )
            market_snapshot[token_address] = closest_point
        
        return market_snapshot
