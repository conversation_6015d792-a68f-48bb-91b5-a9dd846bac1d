"""
Strategy Tester for NewQuantAgent Backtesting

Tests different trading strategies and parameter combinations to find optimal configurations.
Provides strategy comparison and optimization capabilities.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
import itertools

from .backtest_engine import BacktestEngine, BacktestConfig, BacktestResult
from .performance_analyzer import PerformanceAnalyzer, BacktestMetrics

logger = logging.getLogger(__name__)

@dataclass
class StrategyResult:
    """Results from testing a specific strategy configuration"""
    strategy_name: str
    parameters: Dict[str, Any]
    backtest_result: BacktestResult
    performance_score: float = 0.0
    rank: int = 0

@dataclass
class StrategyParameter:
    """Definition of a strategy parameter for optimization"""
    name: str
    values: List[Any]
    description: str = ""

class StrategyTester:
    """
    Tests different trading strategies and parameter combinations.
    
    Features:
    - Strategy parameter optimization
    - Multi-strategy comparison
    - Performance ranking
    - Statistical significance testing
    - Walk-forward analysis
    """
    
    def __init__(self):
        self.performance_analyzer = PerformanceAnalyzer()
        logger.info("Strategy tester initialized")
    
    async def test_parameter_optimization(
        self,
        base_config: BacktestConfig,
        parameters: List[StrategyParameter],
        max_combinations: int = 50
    ) -> List[StrategyResult]:
        """
        Test different parameter combinations to find optimal settings.
        
        Args:
            base_config: Base backtest configuration
            parameters: List of parameters to optimize
            max_combinations: Maximum number of combinations to test
            
        Returns:
            List of strategy results ranked by performance
        """
        
        logger.info(f"🔍 Starting parameter optimization")
        logger.info(f"📊 Parameters to optimize: {[p.name for p in parameters]}")
        
        # Generate all parameter combinations
        param_names = [p.name for p in parameters]
        param_values = [p.values for p in parameters]
        all_combinations = list(itertools.product(*param_values))
        
        # Limit combinations if too many
        if len(all_combinations) > max_combinations:
            logger.warning(f"Too many combinations ({len(all_combinations)}), sampling {max_combinations}")
            import random
            all_combinations = random.sample(all_combinations, max_combinations)
        
        logger.info(f"🧪 Testing {len(all_combinations)} parameter combinations")
        
        results = []
        
        for i, combination in enumerate(all_combinations):
            # Create parameter dictionary
            params = dict(zip(param_names, combination))
            
            logger.info(f"Testing combination {i+1}/{len(all_combinations)}: {params}")
            
            # Create modified config
            test_config = self._create_config_with_parameters(base_config, params)
            
            # Run backtest
            try:
                engine = BacktestEngine(test_config)
                backtest_result = await engine.run_backtest()
                
                if backtest_result.success:
                    # Calculate performance score
                    score = self._calculate_performance_score(backtest_result.metrics)
                    
                    result = StrategyResult(
                        strategy_name=f"Optimization_{i+1}",
                        parameters=params,
                        backtest_result=backtest_result,
                        performance_score=score
                    )
                    
                    results.append(result)
                    
                    logger.info(f"✅ Combination {i+1}: Score {score:.3f}, Return {backtest_result.metrics.total_return:.2%}")
                else:
                    logger.warning(f"❌ Combination {i+1} failed: {backtest_result.error_message}")
                    
            except Exception as e:
                logger.error(f"❌ Error testing combination {i+1}: {e}")
        
        # Rank results by performance score
        results.sort(key=lambda x: x.performance_score, reverse=True)
        for i, result in enumerate(results):
            result.rank = i + 1
        
        logger.info(f"🏆 Parameter optimization completed. Best score: {results[0].performance_score:.3f}")
        
        return results
    
    async def test_strategy_comparison(
        self,
        strategies: Dict[str, BacktestConfig]
    ) -> List[StrategyResult]:
        """
        Compare different trading strategies.
        
        Args:
            strategies: Dictionary of strategy name to config
            
        Returns:
            List of strategy results ranked by performance
        """
        
        logger.info(f"🔄 Comparing {len(strategies)} trading strategies")
        
        results = []
        
        for strategy_name, config in strategies.items():
            logger.info(f"Testing strategy: {strategy_name}")
            
            try:
                engine = BacktestEngine(config)
                backtest_result = await engine.run_backtest()
                
                if backtest_result.success:
                    score = self._calculate_performance_score(backtest_result.metrics)
                    
                    result = StrategyResult(
                        strategy_name=strategy_name,
                        parameters={},  # No specific parameters for strategy comparison
                        backtest_result=backtest_result,
                        performance_score=score
                    )
                    
                    results.append(result)
                    
                    logger.info(f"✅ {strategy_name}: Score {score:.3f}, Return {backtest_result.metrics.total_return:.2%}")
                else:
                    logger.warning(f"❌ {strategy_name} failed: {backtest_result.error_message}")
                    
            except Exception as e:
                logger.error(f"❌ Error testing {strategy_name}: {e}")
        
        # Rank results
        results.sort(key=lambda x: x.performance_score, reverse=True)
        for i, result in enumerate(results):
            result.rank = i + 1
        
        logger.info(f"🏆 Strategy comparison completed. Best: {results[0].strategy_name}")
        
        return results
    
    async def walk_forward_analysis(
        self,
        base_config: BacktestConfig,
        optimization_window_days: int = 30,
        test_window_days: int = 7,
        parameters: List[StrategyParameter] = None
    ) -> Dict[str, Any]:
        """
        Perform walk-forward analysis to test strategy robustness.
        
        Args:
            base_config: Base configuration
            optimization_window_days: Days to use for optimization
            test_window_days: Days to use for testing
            parameters: Parameters to optimize in each window
            
        Returns:
            Walk-forward analysis results
        """
        
        logger.info("🚶 Starting walk-forward analysis")
        
        total_days = (base_config.end_date - base_config.start_date).days
        step_size = test_window_days
        
        results = []
        current_date = base_config.start_date
        
        while current_date + timedelta(days=optimization_window_days + test_window_days) <= base_config.end_date:
            # Define optimization and test periods
            opt_start = current_date
            opt_end = current_date + timedelta(days=optimization_window_days)
            test_start = opt_end
            test_end = opt_end + timedelta(days=test_window_days)
            
            logger.info(f"📊 Optimization: {opt_start.date()} to {opt_end.date()}")
            logger.info(f"🧪 Testing: {test_start.date()} to {test_end.date()}")
            
            try:
                # Optimization phase
                opt_config = BacktestConfig(
                    start_date=opt_start,
                    end_date=opt_end,
                    initial_capital=base_config.initial_capital,
                    interval_minutes=base_config.interval_minutes,
                    token_count=base_config.token_count,
                    max_positions=base_config.max_positions,
                    enable_ai_analysis=False  # Faster optimization
                )
                
                if parameters:
                    # Optimize parameters on this window
                    opt_results = await self.test_parameter_optimization(
                        opt_config, parameters, max_combinations=20
                    )
                    
                    if opt_results:
                        best_params = opt_results[0].parameters
                    else:
                        best_params = {}
                else:
                    best_params = {}
                
                # Test phase with optimized parameters
                test_config = BacktestConfig(
                    start_date=test_start,
                    end_date=test_end,
                    initial_capital=base_config.initial_capital,
                    interval_minutes=base_config.interval_minutes,
                    token_count=base_config.token_count,
                    max_positions=base_config.max_positions,
                    enable_ai_analysis=base_config.enable_ai_analysis
                )
                
                test_config = self._create_config_with_parameters(test_config, best_params)
                
                engine = BacktestEngine(test_config)
                test_result = await engine.run_backtest()
                
                if test_result.success:
                    results.append({
                        'optimization_period': (opt_start, opt_end),
                        'test_period': (test_start, test_end),
                        'optimized_parameters': best_params,
                        'test_result': test_result,
                        'return': test_result.metrics.total_return,
                        'sharpe': test_result.metrics.sharpe_ratio
                    })
                    
                    logger.info(f"✅ Period return: {test_result.metrics.total_return:.2%}")
                
            except Exception as e:
                logger.error(f"❌ Error in walk-forward period: {e}")
            
            current_date += timedelta(days=step_size)
        
        # Calculate aggregate statistics
        if results:
            returns = [r['return'] for r in results]
            sharpes = [r['sharpe'] for r in results if r['sharpe'] != 0]
            
            aggregate_stats = {
                'total_periods': len(results),
                'avg_return': np.mean(returns),
                'return_std': np.std(returns),
                'avg_sharpe': np.mean(sharpes) if sharpes else 0,
                'positive_periods': sum(1 for r in returns if r > 0),
                'win_rate': sum(1 for r in returns if r > 0) / len(returns) if returns else 0
            }
            
            logger.info(f"🏆 Walk-forward completed: {aggregate_stats['win_rate']:.2%} win rate")
            
            return {
                'results': results,
                'aggregate_stats': aggregate_stats
            }
        
        return {'results': [], 'aggregate_stats': {}}
    
    def _create_config_with_parameters(
        self, 
        base_config: BacktestConfig, 
        parameters: Dict[str, Any]
    ) -> BacktestConfig:
        """Create a new config with modified parameters"""
        
        # Create a copy of the base config
        new_config = BacktestConfig(
            start_date=base_config.start_date,
            end_date=base_config.end_date,
            initial_capital=base_config.initial_capital,
            interval_minutes=base_config.interval_minutes,
            token_count=base_config.token_count,
            max_positions=base_config.max_positions,
            position_size=base_config.position_size,
            transaction_cost=base_config.transaction_cost,
            slippage=base_config.slippage,
            enable_ai_analysis=base_config.enable_ai_analysis,
            enable_social_data=base_config.enable_social_data
        )
        
        # Apply parameter modifications
        for param_name, param_value in parameters.items():
            if hasattr(new_config, param_name):
                setattr(new_config, param_name, param_value)
        
        return new_config
    
    def _calculate_performance_score(self, metrics: BacktestMetrics) -> float:
        """
        Calculate a composite performance score for ranking strategies.
        
        Combines multiple metrics into a single score for optimization.
        """
        
        # Base score from return
        return_score = metrics.total_return * 100  # Convert to percentage points
        
        # Adjust for risk
        risk_penalty = metrics.max_drawdown * 200  # Penalize drawdown heavily
        
        # Adjust for consistency
        sharpe_bonus = metrics.sharpe_ratio * 20  # Reward good risk-adjusted returns
        
        # Adjust for trade frequency (prefer strategies that trade)
        trade_bonus = min(10, metrics.total_trades / 10)  # Bonus for reasonable trade count
        
        # Win rate bonus
        win_rate_bonus = (metrics.win_rate - 0.5) * 50  # Bonus for >50% win rate
        
        # Calculate composite score
        score = return_score - risk_penalty + sharpe_bonus + trade_bonus + win_rate_bonus
        
        return max(0, score)  # Ensure non-negative score
    
    def generate_optimization_report(self, results: List[StrategyResult]) -> str:
        """Generate optimization results report"""
        
        if not results:
            return "No optimization results to report."
        
        report = []
        report.append("=" * 80)
        report.append("🔍 STRATEGY OPTIMIZATION REPORT")
        report.append("=" * 80)
        
        # Top 10 results
        top_results = results[:10]
        
        report.append(f"\n🏆 TOP {len(top_results)} PARAMETER COMBINATIONS")
        report.append("-" * 80)
        report.append(f"{'Rank':<5} {'Score':<8} {'Return':<10} {'Sharpe':<8} {'Win Rate':<10} {'Parameters'}")
        report.append("-" * 80)
        
        for result in top_results:
            metrics = result.backtest_result.metrics
            params_str = ", ".join([f"{k}={v}" for k, v in result.parameters.items()])
            
            report.append(f"{result.rank:<5} {result.performance_score:<8.2f} "
                         f"{metrics.total_return:<9.2%} {metrics.sharpe_ratio:<8.3f} "
                         f"{metrics.win_rate:<9.2%} {params_str}")
        
        # Best configuration details
        best = results[0]
        report.append(f"\n🌟 BEST CONFIGURATION DETAILS")
        report.append("-" * 40)
        report.append(f"Performance Score: {best.performance_score:.2f}")
        report.append(f"Total Return: {best.backtest_result.metrics.total_return:.2%}")
        report.append(f"Sharpe Ratio: {best.backtest_result.metrics.sharpe_ratio:.3f}")
        report.append(f"Max Drawdown: {best.backtest_result.metrics.max_drawdown:.2%}")
        report.append(f"Win Rate: {best.backtest_result.metrics.win_rate:.2%}")
        report.append(f"Total Trades: {best.backtest_result.metrics.total_trades}")
        
        report.append(f"\nOptimal Parameters:")
        for param, value in best.parameters.items():
            report.append(f"  {param}: {value}")
        
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)
