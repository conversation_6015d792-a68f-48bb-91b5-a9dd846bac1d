"""
Performance Analyzer for NewQuantAgent Backtesting

Comprehensive performance analysis and metrics calculation for backtesting results.
Provides detailed statistics, risk metrics, and performance attribution.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from decimal import Decimal
import math

logger = logging.getLogger(__name__)

@dataclass
class BacktestMetrics:
    """Comprehensive backtest performance metrics"""
    
    # Basic Performance
    initial_value: float = 0.0
    final_value: float = 0.0
    total_return: float = 0.0
    annualized_return: float = 0.0
    
    # Trade Statistics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    
    # Profit/Loss Metrics
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    net_profit: float = 0.0
    profit_factor: float = 0.0
    
    # Risk Metrics
    max_drawdown: float = 0.0
    max_drawdown_duration: float = 0.0
    volatility: float = 0.0
    downside_deviation: float = 0.0
    
    # Risk-Adjusted Returns
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # Trade Analysis
    avg_trade_return: float = 0.0
    avg_winning_trade: float = 0.0
    avg_losing_trade: float = 0.0
    largest_winning_trade: float = 0.0
    largest_losing_trade: float = 0.0
    avg_trade_duration: float = 0.0
    
    # Additional Metrics
    recovery_factor: float = 0.0
    expectancy: float = 0.0
    kelly_criterion: float = 0.0
    
    # Execution Metrics
    signals_generated: int = 0
    signals_executed: int = 0
    execution_rate: float = 0.0

class PerformanceAnalyzer:
    """
    Analyzes backtest performance and calculates comprehensive metrics.
    
    Features:
    - Complete performance statistics
    - Risk-adjusted return metrics
    - Drawdown analysis
    - Trade attribution
    - Statistical significance testing
    """
    
    def __init__(self):
        self.RISK_FREE_RATE = 0.02  # 2% annual risk-free rate
        self.TRADING_DAYS_PER_YEAR = 365  # Crypto trades 24/7
        
        logger.info("Performance analyzer initialized")
    
    def calculate_backtest_metrics(
        self,
        trades: List[Dict[str, Any]],
        portfolio_history: List[Dict[str, Any]],
        initial_capital: float
    ) -> BacktestMetrics:
        """
        Calculate comprehensive backtest metrics.
        
        Args:
            trades: List of executed trades
            portfolio_history: Portfolio value over time
            initial_capital: Starting capital
            
        Returns:
            BacktestMetrics with all calculated metrics
        """
        
        logger.info("Calculating comprehensive backtest metrics...")
        
        metrics = BacktestMetrics()
        
        if not trades or not portfolio_history:
            logger.warning("No trades or portfolio history provided")
            return metrics
        
        # Basic performance metrics
        metrics.initial_value = initial_capital
        metrics.final_value = portfolio_history[-1]['total_value']
        metrics.total_return = (metrics.final_value / metrics.initial_value) - 1
        
        # Calculate annualized return
        if portfolio_history:
            start_time = portfolio_history[0]['timestamp']
            end_time = portfolio_history[-1]['timestamp']
            years = (end_time - start_time) / (365.25 * 24 * 3600)  # Convert seconds to years
            
            if years > 0:
                metrics.annualized_return = (metrics.final_value / metrics.initial_value) ** (1/years) - 1
        
        # Trade statistics
        metrics = self._calculate_trade_statistics(metrics, trades)
        
        # Risk metrics
        metrics = self._calculate_risk_metrics(metrics, portfolio_history)
        
        # Risk-adjusted returns
        metrics = self._calculate_risk_adjusted_returns(metrics, portfolio_history)
        
        # Additional analysis
        metrics = self._calculate_additional_metrics(metrics, trades, portfolio_history)
        
        logger.info("✅ Performance metrics calculation completed")
        return metrics
    
    def _calculate_trade_statistics(
        self, 
        metrics: BacktestMetrics, 
        trades: List[Dict[str, Any]]
    ) -> BacktestMetrics:
        """Calculate trade-related statistics"""
        
        # Filter for completed trades (SELL orders)
        completed_trades = [t for t in trades if t['action'] == 'SELL']
        
        metrics.total_trades = len(completed_trades)
        
        if not completed_trades:
            return metrics
        
        # Analyze trade outcomes
        trade_returns = []
        winning_trades = []
        losing_trades = []
        trade_durations = []
        
        for trade in completed_trades:
            pnl = trade.get('pnl', 0)
            pnl_percent = trade.get('pnl_percent', 0)
            duration = trade.get('hold_time', 0)
            
            trade_returns.append(pnl_percent)
            trade_durations.append(duration)
            
            if pnl > 0:
                winning_trades.append(pnl)
                metrics.winning_trades += 1
            else:
                losing_trades.append(abs(pnl))
                metrics.losing_trades += 1
        
        # Calculate statistics
        if metrics.total_trades > 0:
            metrics.win_rate = metrics.winning_trades / metrics.total_trades
        
        if winning_trades:
            metrics.gross_profit = sum(winning_trades)
            metrics.avg_winning_trade = np.mean(winning_trades)
            metrics.largest_winning_trade = max(winning_trades)
        
        if losing_trades:
            metrics.gross_loss = sum(losing_trades)
            metrics.avg_losing_trade = -np.mean(losing_trades)
            metrics.largest_losing_trade = -max(losing_trades)
        
        metrics.net_profit = metrics.gross_profit - metrics.gross_loss
        
        if metrics.gross_loss > 0:
            metrics.profit_factor = metrics.gross_profit / metrics.gross_loss
        
        if trade_returns:
            metrics.avg_trade_return = np.mean(trade_returns)
        
        if trade_durations:
            metrics.avg_trade_duration = np.mean(trade_durations) / 3600  # Convert to hours
        
        return metrics
    
    def _calculate_risk_metrics(
        self, 
        metrics: BacktestMetrics, 
        portfolio_history: List[Dict[str, Any]]
    ) -> BacktestMetrics:
        """Calculate risk-related metrics"""
        
        if len(portfolio_history) < 2:
            return metrics
        
        # Extract portfolio values and calculate returns
        values = [p['total_value'] for p in portfolio_history]
        returns = []
        
        for i in range(1, len(values)):
            ret = (values[i] / values[i-1]) - 1
            returns.append(ret)
        
        if not returns:
            return metrics
        
        # Volatility (annualized)
        returns_array = np.array(returns)
        metrics.volatility = np.std(returns_array) * np.sqrt(self.TRADING_DAYS_PER_YEAR * 24)  # Hourly to annual
        
        # Downside deviation
        negative_returns = [r for r in returns if r < 0]
        if negative_returns:
            metrics.downside_deviation = np.std(negative_returns) * np.sqrt(self.TRADING_DAYS_PER_YEAR * 24)
        
        # Maximum drawdown
        peak = values[0]
        max_dd = 0
        dd_start = 0
        dd_end = 0
        current_dd_start = 0
        
        for i, value in enumerate(values):
            if value > peak:
                peak = value
                current_dd_start = i
            else:
                drawdown = (peak - value) / peak
                if drawdown > max_dd:
                    max_dd = drawdown
                    dd_start = current_dd_start
                    dd_end = i
        
        metrics.max_drawdown = max_dd
        
        # Drawdown duration (in hours)
        if dd_end > dd_start and portfolio_history:
            start_time = portfolio_history[dd_start]['timestamp']
            end_time = portfolio_history[dd_end]['timestamp']
            metrics.max_drawdown_duration = (end_time - start_time) / 3600
        
        return metrics
    
    def _calculate_risk_adjusted_returns(
        self, 
        metrics: BacktestMetrics, 
        portfolio_history: List[Dict[str, Any]]
    ) -> BacktestMetrics:
        """Calculate risk-adjusted return metrics"""
        
        if len(portfolio_history) < 2:
            return metrics
        
        # Calculate returns
        values = [p['total_value'] for p in portfolio_history]
        returns = []
        
        for i in range(1, len(values)):
            ret = (values[i] / values[i-1]) - 1
            returns.append(ret)
        
        if not returns:
            return metrics
        
        returns_array = np.array(returns)
        
        # Sharpe Ratio
        if metrics.volatility > 0:
            excess_return = metrics.annualized_return - self.RISK_FREE_RATE
            metrics.sharpe_ratio = excess_return / metrics.volatility
        
        # Sortino Ratio
        if metrics.downside_deviation > 0:
            excess_return = metrics.annualized_return - self.RISK_FREE_RATE
            metrics.sortino_ratio = excess_return / metrics.downside_deviation
        
        # Calmar Ratio
        if metrics.max_drawdown > 0:
            metrics.calmar_ratio = metrics.annualized_return / metrics.max_drawdown
        
        return metrics
    
    def _calculate_additional_metrics(
        self, 
        metrics: BacktestMetrics, 
        trades: List[Dict[str, Any]],
        portfolio_history: List[Dict[str, Any]]
    ) -> BacktestMetrics:
        """Calculate additional performance metrics"""
        
        # Recovery Factor
        if metrics.max_drawdown > 0:
            metrics.recovery_factor = metrics.net_profit / (metrics.initial_value * metrics.max_drawdown)
        
        # Expectancy
        if metrics.total_trades > 0:
            avg_win = metrics.avg_winning_trade if metrics.avg_winning_trade else 0
            avg_loss = abs(metrics.avg_losing_trade) if metrics.avg_losing_trade else 0
            
            metrics.expectancy = (metrics.win_rate * avg_win) - ((1 - metrics.win_rate) * avg_loss)
        
        # Kelly Criterion
        if metrics.win_rate > 0 and metrics.avg_losing_trade < 0:
            win_loss_ratio = abs(metrics.avg_winning_trade / metrics.avg_losing_trade) if metrics.avg_losing_trade != 0 else 0
            metrics.kelly_criterion = metrics.win_rate - ((1 - metrics.win_rate) / win_loss_ratio)
        
        return metrics
    
    def generate_performance_report(self, metrics: BacktestMetrics) -> str:
        """Generate a comprehensive performance report"""
        
        report = []
        report.append("=" * 80)
        report.append("📊 NEWQUANTAGENT BACKTEST PERFORMANCE REPORT")
        report.append("=" * 80)
        
        # Basic Performance
        report.append("\n💰 BASIC PERFORMANCE")
        report.append("-" * 40)
        report.append(f"Initial Capital:        ${metrics.initial_value:,.2f}")
        report.append(f"Final Value:           ${metrics.final_value:,.2f}")
        report.append(f"Total Return:          {metrics.total_return:.2%}")
        report.append(f"Annualized Return:     {metrics.annualized_return:.2%}")
        
        # Trade Statistics
        report.append("\n📈 TRADE STATISTICS")
        report.append("-" * 40)
        report.append(f"Total Trades:          {metrics.total_trades}")
        report.append(f"Winning Trades:        {metrics.winning_trades}")
        report.append(f"Losing Trades:         {metrics.losing_trades}")
        report.append(f"Win Rate:              {metrics.win_rate:.2%}")
        report.append(f"Avg Trade Return:      {metrics.avg_trade_return:.2%}")
        report.append(f"Avg Trade Duration:    {metrics.avg_trade_duration:.1f} hours")
        
        # Profit/Loss Analysis
        report.append("\n💵 PROFIT/LOSS ANALYSIS")
        report.append("-" * 40)
        report.append(f"Gross Profit:          ${metrics.gross_profit:,.2f}")
        report.append(f"Gross Loss:            ${metrics.gross_loss:,.2f}")
        report.append(f"Net Profit:            ${metrics.net_profit:,.2f}")
        report.append(f"Profit Factor:         {metrics.profit_factor:.2f}")
        report.append(f"Avg Winning Trade:     ${metrics.avg_winning_trade:,.2f}")
        report.append(f"Avg Losing Trade:      ${metrics.avg_losing_trade:,.2f}")
        report.append(f"Largest Win:           ${metrics.largest_winning_trade:,.2f}")
        report.append(f"Largest Loss:          ${metrics.largest_losing_trade:,.2f}")
        
        # Risk Metrics
        report.append("\n⚠️  RISK METRICS")
        report.append("-" * 40)
        report.append(f"Max Drawdown:          {metrics.max_drawdown:.2%}")
        report.append(f"Drawdown Duration:     {metrics.max_drawdown_duration:.1f} hours")
        report.append(f"Volatility (Annual):   {metrics.volatility:.2%}")
        report.append(f"Downside Deviation:    {metrics.downside_deviation:.2%}")
        
        # Risk-Adjusted Returns
        report.append("\n📊 RISK-ADJUSTED RETURNS")
        report.append("-" * 40)
        report.append(f"Sharpe Ratio:          {metrics.sharpe_ratio:.3f}")
        report.append(f"Sortino Ratio:         {metrics.sortino_ratio:.3f}")
        report.append(f"Calmar Ratio:          {metrics.calmar_ratio:.3f}")
        
        # Additional Metrics
        report.append("\n🔍 ADDITIONAL METRICS")
        report.append("-" * 40)
        report.append(f"Recovery Factor:       {metrics.recovery_factor:.2f}")
        report.append(f"Expectancy:            ${metrics.expectancy:.2f}")
        report.append(f"Kelly Criterion:       {metrics.kelly_criterion:.2%}")
        
        # Performance Rating
        report.append("\n⭐ PERFORMANCE RATING")
        report.append("-" * 40)
        rating = self._calculate_performance_rating(metrics)
        report.append(f"Overall Rating:        {rating}/10")
        report.append(self._get_rating_explanation(rating))
        
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)
    
    def _calculate_performance_rating(self, metrics: BacktestMetrics) -> int:
        """Calculate overall performance rating (1-10)"""
        
        score = 0
        
        # Return score (0-3 points)
        if metrics.annualized_return > 0.5:  # >50% annual return
            score += 3
        elif metrics.annualized_return > 0.2:  # >20% annual return
            score += 2
        elif metrics.annualized_return > 0:  # Positive return
            score += 1
        
        # Win rate score (0-2 points)
        if metrics.win_rate > 0.6:  # >60% win rate
            score += 2
        elif metrics.win_rate > 0.5:  # >50% win rate
            score += 1
        
        # Risk score (0-3 points)
        if metrics.max_drawdown < 0.1:  # <10% max drawdown
            score += 3
        elif metrics.max_drawdown < 0.2:  # <20% max drawdown
            score += 2
        elif metrics.max_drawdown < 0.3:  # <30% max drawdown
            score += 1
        
        # Sharpe ratio score (0-2 points)
        if metrics.sharpe_ratio > 2.0:  # Excellent Sharpe
            score += 2
        elif metrics.sharpe_ratio > 1.0:  # Good Sharpe
            score += 1
        
        return min(10, max(1, score))
    
    def _get_rating_explanation(self, rating: int) -> str:
        """Get explanation for performance rating"""
        
        if rating >= 9:
            return "🌟 EXCEPTIONAL - Outstanding performance with excellent risk management"
        elif rating >= 7:
            return "🚀 EXCELLENT - Strong performance with good risk control"
        elif rating >= 5:
            return "✅ GOOD - Solid performance, room for improvement"
        elif rating >= 3:
            return "⚠️  FAIR - Marginal performance, needs optimization"
        else:
            return "❌ POOR - Significant improvements needed"
