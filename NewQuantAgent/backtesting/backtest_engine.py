"""
Backtest Engine for NewQuantAgent

Main backtesting engine that orchestrates strategy testing against historical data.
Provides comprehensive backtesting capabilities with realistic market simulation.
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from decimal import Decimal

from .historical_data_simulator import HistoricalDataSimulator, MarketDataPoint
from .performance_analyzer import PerformanceAnalyzer, BacktestMetrics

# Import NewQuantAgent components for testing
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.mathematical_engine import MathematicalEngine
from core.ai_integration import AIIntegrationSystem
from core.trading_engine import TradingEngine
from core.risk_management import RiskManagementSystem
from config.settings import Config

logger = logging.getLogger(__name__)

@dataclass
class BacktestConfig:
    """Configuration for backtesting"""
    start_date: datetime
    end_date: datetime
    initial_capital: float = 10000.0
    interval_minutes: int = 60
    token_count: int = 50
    max_positions: int = 5
    position_size: float = 0.02  # 2% per position
    transaction_cost: float = 0.003  # 0.3% transaction cost
    slippage: float = 0.001  # 0.1% slippage
    enable_ai_analysis: bool = False  # Disable AI for faster backtesting
    enable_social_data: bool = False  # Disable social data for faster backtesting

@dataclass
class BacktestResult:
    """Results from a backtest run"""
    config: BacktestConfig
    metrics: BacktestMetrics
    trades: List[Dict[str, Any]] = field(default_factory=list)
    portfolio_history: List[Dict[str, Any]] = field(default_factory=list)
    signals_generated: int = 0
    signals_executed: int = 0
    execution_time: float = 0.0
    success: bool = True
    error_message: Optional[str] = None

class BacktestEngine:
    """
    Main backtesting engine for NewQuantAgent.
    
    Features:
    - Historical data simulation
    - Strategy testing with real NewQuantAgent logic
    - Performance analysis and metrics
    - Trade execution simulation
    - Risk management validation
    """
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.data_simulator = HistoricalDataSimulator()
        self.performance_analyzer = PerformanceAnalyzer()
        
        # Initialize NewQuantAgent components for testing
        self.agent_config = Config()
        self.math_engine = MathematicalEngine()

        # Only initialize AI if enabled (for faster backtesting)
        if config.enable_ai_analysis:
            self.ai_system = AIIntegrationSystem(self.agent_config)
        else:
            self.ai_system = None

        self.trading_engine = TradingEngine(self.agent_config)
        self.risk_system = RiskManagementSystem(self.agent_config)
        
        # Backtest state
        self.current_capital = config.initial_capital
        self.positions = {}
        self.trade_history = []
        self.portfolio_history = []
        self.signals_generated = 0
        self.signals_executed = 0
        
        logger.info(f"Backtest engine initialized with ${config.initial_capital:,.2f} capital")
    
    async def run_backtest(self) -> BacktestResult:
        """
        Run complete backtest simulation.
        
        Returns:
            BacktestResult with comprehensive performance metrics
        """
        start_time = time.time()
        
        try:
            logger.info("🚀 Starting NewQuantAgent Backtest")
            logger.info(f"📅 Period: {self.config.start_date} to {self.config.end_date}")
            logger.info(f"💰 Initial Capital: ${self.config.initial_capital:,.2f}")
            logger.info(f"🎯 Testing {self.config.token_count} tokens")
            
            # Step 1: Generate historical data
            logger.info("📊 Generating historical market data...")
            token_addresses = [f"token_{i:03d}" for i in range(self.config.token_count)]
            
            historical_data = await self.data_simulator.generate_historical_data(
                token_addresses,
                self.config.start_date,
                self.config.end_date,
                self.config.interval_minutes
            )
            
            logger.info(f"✅ Generated historical data for {len(historical_data)} tokens")
            
            # Step 2: Run simulation
            await self._run_simulation(historical_data)
            
            # Step 3: Calculate performance metrics
            logger.info("📈 Calculating performance metrics...")
            metrics = self.performance_analyzer.calculate_backtest_metrics(
                self.trade_history,
                self.portfolio_history,
                self.config.initial_capital
            )
            
            execution_time = time.time() - start_time
            
            # Create result
            result = BacktestResult(
                config=self.config,
                metrics=metrics,
                trades=self.trade_history,
                portfolio_history=self.portfolio_history,
                signals_generated=self.signals_generated,
                signals_executed=self.signals_executed,
                execution_time=execution_time,
                success=True
            )
            
            logger.info("🎉 Backtest completed successfully!")
            self._log_summary(result)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ Backtest failed: {e}")
            
            return BacktestResult(
                config=self.config,
                metrics=BacktestMetrics(),  # Empty metrics
                execution_time=execution_time,
                success=False,
                error_message=str(e)
            )
    
    async def _run_simulation(self, historical_data: Dict[str, List[MarketDataPoint]]):
        """Run the main simulation loop"""
        
        # Get all timestamps
        all_timestamps = set()
        for data_points in historical_data.values():
            for point in data_points:
                all_timestamps.add(point.timestamp)
        
        sorted_timestamps = sorted(all_timestamps)
        logger.info(f"🔄 Running simulation over {len(sorted_timestamps)} time steps")
        
        for i, timestamp in enumerate(sorted_timestamps):
            if i % 100 == 0:
                progress = (i / len(sorted_timestamps)) * 100
                logger.info(f"Progress: {progress:.1f}% ({i}/{len(sorted_timestamps)})")
            
            # Get market data at this timestamp
            market_snapshot = await self.data_simulator.get_market_data_at_time(
                historical_data, timestamp
            )
            
            # Run trading cycle
            await self._execute_trading_cycle(timestamp, market_snapshot)
            
            # Record portfolio state
            self._record_portfolio_state(timestamp)
    
    async def _execute_trading_cycle(self, timestamp: float, market_data: Dict[str, MarketDataPoint]):
        """Execute a single trading cycle"""
        
        try:
            # Step 1: Analyze tokens and generate signals
            signals = []
            
            for token_address, data_point in market_data.items():
                # Skip if already have position and at max positions
                if (len(self.positions) >= self.config.max_positions and 
                    token_address not in self.positions):
                    continue
                
                # Generate signal using NewQuantAgent logic
                signal = await self._generate_signal(token_address, data_point, market_data)
                
                if signal:
                    signals.append(signal)
                    self.signals_generated += 1
            
            # Step 2: Execute trades based on signals
            for signal in signals:
                await self._execute_signal(timestamp, signal, market_data)
            
            # Step 3: Update existing positions
            await self._update_positions(timestamp, market_data)
            
        except Exception as e:
            logger.error(f"Error in trading cycle at {timestamp}: {e}")
    
    async def _generate_signal(
        self, 
        token_address: str, 
        data_point: MarketDataPoint,
        market_data: Dict[str, MarketDataPoint]
    ) -> Optional[Dict[str, Any]]:
        """Generate trading signal using NewQuantAgent logic"""
        
        try:
            # Create price history for mathematical analysis
            price_history = [data_point.price]  # Simplified for backtesting
            
            # Mathematical analysis
            math_analysis = await self._analyze_mathematically(token_address, price_history)
            
            # AI analysis (if enabled)
            ai_analysis = {}
            if self.ai_system and self.config.enable_ai_analysis:
                ai_analysis = await self._analyze_with_ai(token_address, data_point)
            
            # Trading analysis
            trading_analysis = await self._analyze_trading_metrics(token_address, data_point)
            
            # Generate composite signal (simplified version of main logic)
            signal_score = self._calculate_composite_score(
                math_analysis, ai_analysis, trading_analysis
            )
            
            # Determine signal type
            if signal_score >= 70:
                signal_type = "BUY"
            elif signal_score <= 30:
                signal_type = "SELL"
            else:
                return None  # HOLD
            
            # Calculate confidence
            confidence = signal_score / 100.0
            
            if confidence < 0.6:
                return None
            
            return {
                'token_address': token_address,
                'signal_type': signal_type,
                'confidence': confidence,
                'score': signal_score,
                'price': data_point.price,
                'analysis': {
                    'mathematical': math_analysis,
                    'ai': ai_analysis,
                    'trading': trading_analysis
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating signal for {token_address}: {e}")
            return None
    
    async def _analyze_mathematically(self, token_address: str, price_history: List[float]) -> Dict[str, Any]:
        """Simplified mathematical analysis for backtesting"""
        
        if len(price_history) < 2:
            return {'score': 50, 'regime': 'UNKNOWN'}
        
        # Simple momentum calculation
        momentum = (price_history[-1] / price_history[0] - 1) * 100
        
        # Convert to score (0-100)
        score = max(0, min(100, 50 + momentum * 2))
        
        return {
            'score': score,
            'momentum': momentum,
            'regime': 'TRENDING' if momentum > 5 else 'MEAN_REVERTING' if momentum < -5 else 'RANDOM_WALK'
        }
    
    async def _analyze_with_ai(self, token_address: str, data_point: MarketDataPoint) -> Dict[str, Any]:
        """Simplified AI analysis for backtesting"""
        
        # Simulate AI analysis based on social metrics
        social_score = min(100, data_point.social_mentions / 10)
        dev_score = data_point.dev_activity * 100
        
        composite_ai_score = (social_score * 0.6 + dev_score * 0.4)
        
        return {
            'score': composite_ai_score,
            'social_sentiment': social_score / 100,
            'confidence': 0.7
        }
    
    async def _analyze_trading_metrics(self, token_address: str, data_point: MarketDataPoint) -> Dict[str, Any]:
        """Analyze trading-specific metrics"""
        
        # Bonding curve analysis
        graduation_score = data_point.bonding_curve_progress * 50
        
        # Liquidity analysis
        liquidity_score = min(50, data_point.liquidity_usd / 1000)
        
        # Volume analysis
        volume_score = min(30, data_point.volume_24h / 10000)
        
        total_score = graduation_score + liquidity_score + volume_score
        
        return {
            'score': total_score,
            'graduation_probability': data_point.bonding_curve_progress,
            'liquidity_score': liquidity_score,
            'volume_score': volume_score
        }
    
    def _calculate_composite_score(
        self, 
        math_analysis: Dict[str, Any], 
        ai_analysis: Dict[str, Any], 
        trading_analysis: Dict[str, Any]
    ) -> float:
        """Calculate composite signal score"""
        
        math_score = math_analysis.get('score', 50)
        ai_score = ai_analysis.get('score', 50) if ai_analysis else 50
        trading_score = trading_analysis.get('score', 50)
        
        # Weighted combination (same as main system)
        composite_score = (
            math_score * 0.3 +
            ai_score * 0.25 +
            trading_score * 0.45  # Higher weight for trading metrics in backtest
        )
        
        return composite_score
    
    async def _execute_signal(
        self, 
        timestamp: float, 
        signal: Dict[str, Any], 
        market_data: Dict[str, MarketDataPoint]
    ):
        """Execute a trading signal"""
        
        token_address = signal['token_address']
        signal_type = signal['signal_type']
        price = signal['price']
        
        try:
            if signal_type == "BUY" and token_address not in self.positions:
                # Calculate position size
                position_value = self.current_capital * self.config.position_size
                shares = position_value / price
                
                # Apply transaction costs
                total_cost = position_value * (1 + self.config.transaction_cost + self.config.slippage)
                
                if total_cost <= self.current_capital:
                    # Execute buy
                    self.positions[token_address] = {
                        'shares': shares,
                        'entry_price': price,
                        'entry_time': timestamp,
                        'entry_value': position_value
                    }
                    
                    self.current_capital -= total_cost
                    self.signals_executed += 1
                    
                    # Record trade
                    self.trade_history.append({
                        'timestamp': timestamp,
                        'token_address': token_address,
                        'action': 'BUY',
                        'shares': shares,
                        'price': price,
                        'value': position_value,
                        'total_cost': total_cost,
                        'signal_confidence': signal['confidence']
                    })
                    
                    logger.debug(f"BUY {token_address}: {shares:.2f} shares at ${price:.6f}")
            
            elif signal_type == "SELL" and token_address in self.positions:
                # Execute sell
                position = self.positions[token_address]
                shares = position['shares']
                
                # Calculate proceeds
                gross_proceeds = shares * price
                net_proceeds = gross_proceeds * (1 - self.config.transaction_cost - self.config.slippage)
                
                # Calculate P&L
                pnl = net_proceeds - position['entry_value']
                pnl_percent = (pnl / position['entry_value']) * 100
                
                self.current_capital += net_proceeds
                del self.positions[token_address]
                self.signals_executed += 1
                
                # Record trade
                self.trade_history.append({
                    'timestamp': timestamp,
                    'token_address': token_address,
                    'action': 'SELL',
                    'shares': shares,
                    'price': price,
                    'value': gross_proceeds,
                    'net_proceeds': net_proceeds,
                    'pnl': pnl,
                    'pnl_percent': pnl_percent,
                    'hold_time': timestamp - position['entry_time'],
                    'signal_confidence': signal['confidence']
                })
                
                logger.debug(f"SELL {token_address}: {shares:.2f} shares at ${price:.6f}, P&L: ${pnl:.2f} ({pnl_percent:.1f}%)")
        
        except Exception as e:
            logger.error(f"Error executing signal for {token_address}: {e}")
    
    async def _update_positions(self, timestamp: float, market_data: Dict[str, MarketDataPoint]):
        """Update existing positions and check for stop-losses"""
        
        positions_to_close = []
        
        for token_address, position in self.positions.items():
            if token_address in market_data:
                current_price = market_data[token_address].price
                entry_price = position['entry_price']
                
                # Calculate current P&L
                current_value = position['shares'] * current_price
                pnl_percent = (current_value / position['entry_value'] - 1) * 100
                
                # Check stop-loss (5% loss)
                if pnl_percent <= -5.0:
                    positions_to_close.append(token_address)
                
                # Check take-profit (15% gain)
                elif pnl_percent >= 15.0:
                    positions_to_close.append(token_address)
        
        # Close positions that hit stop-loss or take-profit
        for token_address in positions_to_close:
            signal = {
                'token_address': token_address,
                'signal_type': 'SELL',
                'confidence': 1.0,
                'price': market_data[token_address].price
            }
            await self._execute_signal(timestamp, signal, market_data)
    
    def _record_portfolio_state(self, timestamp: float):
        """Record current portfolio state"""
        
        # Calculate total portfolio value
        total_value = self.current_capital
        
        # Add value of open positions (would need current prices)
        position_value = 0
        for position in self.positions.values():
            position_value += position['entry_value']  # Simplified
        
        total_value += position_value
        
        self.portfolio_history.append({
            'timestamp': timestamp,
            'total_value': total_value,
            'cash': self.current_capital,
            'positions_value': position_value,
            'positions_count': len(self.positions)
        })
    
    def _log_summary(self, result: BacktestResult):
        """Log backtest summary"""
        
        metrics = result.metrics
        
        logger.info("📊 BACKTEST SUMMARY")
        logger.info("=" * 50)
        logger.info(f"💰 Final Portfolio Value: ${metrics.final_value:,.2f}")
        logger.info(f"📈 Total Return: {metrics.total_return:.2%}")
        logger.info(f"📊 Total Trades: {len(result.trades)}")
        logger.info(f"🎯 Win Rate: {metrics.win_rate:.2%}")
        logger.info(f"📉 Max Drawdown: {metrics.max_drawdown:.2%}")
        logger.info(f"⚡ Sharpe Ratio: {metrics.sharpe_ratio:.3f}")
        logger.info(f"🔄 Signals Generated: {result.signals_generated}")
        logger.info(f"✅ Signals Executed: {result.signals_executed}")
        logger.info(f"⏱️  Execution Time: {result.execution_time:.1f}s")
        logger.info("=" * 50)
