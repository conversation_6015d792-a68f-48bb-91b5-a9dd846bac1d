#!/usr/bin/env python3
"""
NewQuantAgent Backtest Runner

Comprehensive backtesting script for NewQuantAgent trading system.
Tests the complete trading logic against simulated historical data.
"""

import asyncio
import sys
import logging
import argparse
from pathlib import Path
from datetime import datetime, timedelta

# Add NewQuantAgent to path
sys.path.insert(0, str(Path(__file__).parent / "NewQuantAgent"))

from NewQuantAgent.backtesting import (
    BacktestEngine, 
    BacktestConfig, 
    PerformanceAnalyzer
)

def setup_logging(verbose: bool = False):
    """Setup logging for backtest"""
    level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('backtest.log')
        ]
    )
    
    # Reduce noise from some modules
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)

def create_backtest_configs() -> dict:
    """Create different backtest configurations"""
    
    base_end_date = datetime.now()
    
    configs = {
        'quick': BacktestConfig(
            start_date=base_end_date - timedelta(days=7),
            end_date=base_end_date,
            initial_capital=1000.0,
            interval_minutes=60,
            token_count=10,
            max_positions=3,
            position_size=0.05,  # 5% per position
            enable_ai_analysis=False,
            enable_social_data=False
        ),
        
        'standard': BacktestConfig(
            start_date=base_end_date - timedelta(days=30),
            end_date=base_end_date,
            initial_capital=10000.0,
            interval_minutes=60,
            token_count=50,
            max_positions=5,
            position_size=0.02,  # 2% per position
            enable_ai_analysis=False,
            enable_social_data=False
        ),
        
        'comprehensive': BacktestConfig(
            start_date=base_end_date - timedelta(days=90),
            end_date=base_end_date,
            initial_capital=10000.0,
            interval_minutes=60,
            token_count=100,
            max_positions=8,
            position_size=0.015,  # 1.5% per position
            enable_ai_analysis=True,
            enable_social_data=True
        ),
        
        'stress_test': BacktestConfig(
            start_date=base_end_date - timedelta(days=180),
            end_date=base_end_date,
            initial_capital=10000.0,
            interval_minutes=30,  # Higher frequency
            token_count=200,
            max_positions=10,
            position_size=0.01,  # 1% per position
            enable_ai_analysis=True,
            enable_social_data=True
        )
    }
    
    return configs

async def run_single_backtest(config_name: str, config: BacktestConfig) -> None:
    """Run a single backtest configuration"""
    
    logger = logging.getLogger(__name__)
    
    logger.info(f"🚀 Starting {config_name.upper()} backtest")
    logger.info(f"📅 Period: {config.start_date.strftime('%Y-%m-%d')} to {config.end_date.strftime('%Y-%m-%d')}")
    logger.info(f"💰 Capital: ${config.initial_capital:,.2f}")
    logger.info(f"🎯 Tokens: {config.token_count}")
    
    # Create and run backtest
    engine = BacktestEngine(config)
    result = await engine.run_backtest()
    
    if result.success:
        logger.info(f"✅ {config_name.upper()} backtest completed successfully!")
        
        # Generate performance report
        analyzer = PerformanceAnalyzer()
        report = analyzer.generate_performance_report(result.metrics)
        
        # Save report to file
        report_file = f"backtest_report_{config_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"📊 Performance report saved to: {report_file}")
        
        # Print summary to console
        print("\n" + "="*60)
        print(f"📊 {config_name.upper()} BACKTEST SUMMARY")
        print("="*60)
        print(f"💰 Final Value: ${result.metrics.final_value:,.2f}")
        print(f"📈 Total Return: {result.metrics.total_return:.2%}")
        print(f"📊 Annualized Return: {result.metrics.annualized_return:.2%}")
        print(f"🎯 Win Rate: {result.metrics.win_rate:.2%}")
        print(f"📉 Max Drawdown: {result.metrics.max_drawdown:.2%}")
        print(f"⚡ Sharpe Ratio: {result.metrics.sharpe_ratio:.3f}")
        print(f"🔄 Total Trades: {result.metrics.total_trades}")
        print(f"⏱️  Execution Time: {result.execution_time:.1f}s")
        print("="*60)
        
    else:
        logger.error(f"❌ {config_name.upper()} backtest failed: {result.error_message}")

async def run_comparison_backtest() -> None:
    """Run multiple backtests for comparison"""
    
    logger = logging.getLogger(__name__)
    configs = create_backtest_configs()
    
    logger.info("🔄 Running comparison backtest across multiple configurations...")
    
    results = {}
    
    for config_name, config in configs.items():
        logger.info(f"\n{'='*20} {config_name.upper()} BACKTEST {'='*20}")
        
        try:
            engine = BacktestEngine(config)
            result = await engine.run_backtest()
            results[config_name] = result
            
            if result.success:
                logger.info(f"✅ {config_name} completed: {result.metrics.total_return:.2%} return")
            else:
                logger.error(f"❌ {config_name} failed: {result.error_message}")
                
        except Exception as e:
            logger.error(f"❌ Error in {config_name} backtest: {e}")
    
    # Generate comparison report
    if results:
        print("\n" + "="*80)
        print("📊 BACKTEST COMPARISON SUMMARY")
        print("="*80)
        print(f"{'Config':<15} {'Return':<10} {'Sharpe':<8} {'Win Rate':<10} {'Max DD':<10} {'Trades':<8}")
        print("-"*80)
        
        for config_name, result in results.items():
            if result.success:
                metrics = result.metrics
                print(f"{config_name:<15} {metrics.total_return:>8.2%} {metrics.sharpe_ratio:>7.3f} "
                      f"{metrics.win_rate:>8.2%} {metrics.max_drawdown:>8.2%} {metrics.total_trades:>7}")
        
        print("="*80)

async def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(description='NewQuantAgent Backtesting System')
    parser.add_argument('--config', choices=['quick', 'standard', 'comprehensive', 'stress_test', 'comparison'], 
                       default='standard', help='Backtest configuration to run')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    logger.info("🎯 NewQuantAgent Backtesting System")
    logger.info("=" * 50)
    
    try:
        if args.config == 'comparison':
            await run_comparison_backtest()
        else:
            configs = create_backtest_configs()
            config = configs[args.config]
            await run_single_backtest(args.config, config)
        
        logger.info("🎉 Backtesting completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("🛑 Backtesting interrupted by user")
    except Exception as e:
        logger.error(f"❌ Backtesting failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
