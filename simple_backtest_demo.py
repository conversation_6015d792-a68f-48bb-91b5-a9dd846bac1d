#!/usr/bin/env python3
"""
Simple Backtest Demo for NewQuantAgent

Demonstrates the core backtesting logic without complex imports.
Shows how the buy/sell logic works with simulated data.
"""

import asyncio
import random
import time
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Any
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SimpleMarketData:
    """Simple market data point"""
    timestamp: float
    token_address: str
    price: float
    volume_24h: float
    market_cap: float
    holder_count: int
    bonding_curve_progress: float
    social_mentions: int = 0

@dataclass
class SimpleTrade:
    """Simple trade record"""
    timestamp: float
    token_address: str
    action: str  # BUY/SELL
    price: float
    shares: float
    value: float
    pnl: float = 0.0
    pnl_percent: float = 0.0

class SimpleBacktestEngine:
    """
    Simplified backtest engine demonstrating NewQuantAgent logic.
    
    This shows the core buy/sell decision making process that your
    full system uses, but with simplified components for demonstration.
    """
    
    def __init__(self, initial_capital: float = 1000.0):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}  # token_address -> position info
        self.trades = []
        self.portfolio_history = []
        
        # Trading parameters (same as your live system)
        self.POSITION_SIZE = 0.1  # 10% per position
        self.MAX_POSITIONS = 2
        self.BUY_THRESHOLD = 70   # Same as your system
        self.SELL_THRESHOLD = 30  # Same as your system
        self.MIN_CONFIDENCE = 0.6 # Same as your system
        self.STOP_LOSS = 0.05     # 5% stop loss
        self.TAKE_PROFIT = 0.15   # 15% take profit
        
        logger.info(f"Simple backtest engine initialized with ${initial_capital:,.2f}")
    
    def generate_market_data(self, num_tokens: int = 5, num_hours: int = 72) -> Dict[str, List[SimpleMarketData]]:
        """Generate realistic memecoin market data"""
        
        logger.info(f"Generating market data for {num_tokens} tokens over {num_hours} hours")
        
        market_data = {}
        base_time = time.time() - (num_hours * 3600)
        
        for token_id in range(num_tokens):
            token_address = f"token_{token_id:03d}"
            data_points = []
            
            # Initial token characteristics
            current_price = random.uniform(0.000001, 0.01)
            current_volume = random.uniform(5000, 50000)
            current_holders = random.randint(50, 500)
            bonding_progress = random.uniform(0.1, 0.8)
            
            for hour in range(num_hours):
                timestamp = base_time + (hour * 3600)
                
                # Generate price movement (realistic memecoin volatility)
                price_change = np.random.normal(0, 0.15)  # 15% hourly volatility
                
                # Add occasional pump/dump events
                if random.random() < 0.05:  # 5% chance of pump
                    price_change += random.uniform(0.2, 1.0)  # 20-100% pump
                elif random.random() < 0.03:  # 3% chance of dump
                    price_change -= random.uniform(0.3, 0.7)  # 30-70% dump
                
                current_price = max(0.000001, current_price * (1 + price_change))
                
                # Volume correlates with price movement
                volume_change = abs(price_change) * 2 + np.random.normal(0, 0.3)
                current_volume = max(1000, current_volume * (1 + volume_change))
                
                # Holders grow slowly
                if price_change > 0.1:  # Price up 10%+
                    current_holders += random.randint(1, 5)
                
                # Bonding curve progress
                market_cap = current_price * 1_000_000_000
                bonding_progress = min(1.0, market_cap / 69000)
                
                # Social mentions based on price action
                social_mentions = int(current_holders * (1 + abs(price_change)) * random.uniform(0.5, 1.5))
                
                data_point = SimpleMarketData(
                    timestamp=timestamp,
                    token_address=token_address,
                    price=current_price,
                    volume_24h=current_volume,
                    market_cap=market_cap,
                    holder_count=current_holders,
                    bonding_curve_progress=bonding_progress,
                    social_mentions=social_mentions
                )
                
                data_points.append(data_point)
            
            market_data[token_address] = data_points
        
        return market_data
    
    def generate_trading_signal(self, data_point: SimpleMarketData) -> Dict[str, Any]:
        """
        Generate trading signal using NewQuantAgent logic.
        
        This is a simplified version of your actual signal generation
        that combines mathematical, AI, and trading analysis.
        """
        
        # Mathematical Analysis (simplified Hurst + momentum)
        # In real system: Hurst exponent, calculus momentum, Yang-Zhang volatility
        price_momentum = (data_point.price - 0.005) / 0.005 * 100  # Simplified momentum
        math_score = max(0, min(100, 50 + price_momentum))
        
        # AI Analysis (simplified social sentiment)
        # In real system: DeepSeek AI analysis
        social_score = min(100, data_point.social_mentions / 10)
        ai_score = social_score * 0.8  # Simplified AI confidence
        
        # Trading Analysis (bonding curve + volume)
        # In real system: Full bonding curve analysis, liquidity assessment
        graduation_score = data_point.bonding_curve_progress * 50
        volume_score = min(30, data_point.volume_24h / 1000)
        holder_score = min(20, data_point.holder_count / 25)
        trading_score = graduation_score + volume_score + holder_score
        
        # Composite Score (same weights as your system)
        composite_score = (
            math_score * 0.3 +      # 30% mathematical
            ai_score * 0.25 +       # 25% AI  
            trading_score * 0.45    # 45% trading (higher for backtest)
        )
        
        # Normalize and calculate confidence
        normalized_score = max(0, min(100, composite_score))
        confidence = normalized_score / 100.0
        
        # Generate signal (same logic as your system)
        if normalized_score >= self.BUY_THRESHOLD and confidence >= self.MIN_CONFIDENCE:
            signal_type = "BUY"
        elif normalized_score <= self.SELL_THRESHOLD and confidence >= self.MIN_CONFIDENCE:
            signal_type = "SELL"
        else:
            signal_type = "HOLD"
        
        return {
            'signal_type': signal_type,
            'confidence': confidence,
            'score': normalized_score,
            'components': {
                'math_score': math_score,
                'ai_score': ai_score,
                'trading_score': trading_score
            }
        }
    
    def execute_trade(self, timestamp: float, token_address: str, signal: Dict[str, Any], price: float):
        """Execute a trade based on signal"""
        
        signal_type = signal['signal_type']
        
        if signal_type == "BUY" and token_address not in self.positions:
            # Check if we can open new position
            if len(self.positions) >= self.MAX_POSITIONS:
                return
            
            # Calculate position size
            position_value = self.current_capital * self.POSITION_SIZE
            shares = position_value / price
            
            # Apply transaction costs (0.3%)
            total_cost = position_value * 1.003
            
            if total_cost <= self.current_capital:
                # Execute buy
                self.positions[token_address] = {
                    'shares': shares,
                    'entry_price': price,
                    'entry_time': timestamp,
                    'entry_value': position_value
                }
                
                self.current_capital -= total_cost
                
                trade = SimpleTrade(
                    timestamp=timestamp,
                    token_address=token_address,
                    action='BUY',
                    price=price,
                    shares=shares,
                    value=position_value
                )
                
                self.trades.append(trade)
                logger.info(f"BUY {token_address[-6:]}: {shares:.2f} shares at ${price:.6f}")
        
        elif signal_type == "SELL" and token_address in self.positions:
            # Execute sell
            position = self.positions[token_address]
            shares = position['shares']
            
            # Calculate proceeds (with transaction costs)
            gross_proceeds = shares * price
            net_proceeds = gross_proceeds * 0.997  # 0.3% transaction cost
            
            # Calculate P&L
            pnl = net_proceeds - position['entry_value']
            pnl_percent = (pnl / position['entry_value']) * 100
            
            self.current_capital += net_proceeds
            del self.positions[token_address]
            
            trade = SimpleTrade(
                timestamp=timestamp,
                token_address=token_address,
                action='SELL',
                price=price,
                shares=shares,
                value=gross_proceeds,
                pnl=pnl,
                pnl_percent=pnl_percent
            )
            
            self.trades.append(trade)
            logger.info(f"SELL {token_address[-6:]}: {shares:.2f} shares at ${price:.6f}, P&L: ${pnl:.2f} ({pnl_percent:+.1f}%)")
    
    def check_stop_loss_take_profit(self, timestamp: float, market_data: Dict[str, SimpleMarketData]):
        """Check existing positions for stop-loss or take-profit"""
        
        positions_to_close = []
        
        for token_address, position in self.positions.items():
            if token_address in market_data:
                current_price = market_data[token_address].price
                entry_price = position['entry_price']
                
                # Calculate current P&L
                pnl_percent = (current_price / entry_price - 1)
                
                # Check stop-loss or take-profit
                if pnl_percent <= -self.STOP_LOSS:  # 5% loss
                    positions_to_close.append((token_address, "STOP_LOSS"))
                elif pnl_percent >= self.TAKE_PROFIT:  # 15% gain
                    positions_to_close.append((token_address, "TAKE_PROFIT"))
        
        # Close positions
        for token_address, reason in positions_to_close:
            signal = {'signal_type': 'SELL', 'confidence': 1.0}
            self.execute_trade(timestamp, token_address, signal, market_data[token_address].price)
            logger.info(f"Position closed due to {reason}")
    
    async def run_backtest(self) -> Dict[str, Any]:
        """Run the complete backtest simulation"""
        
        logger.info("🚀 Starting simplified backtest simulation...")
        
        # Generate market data
        market_data = self.generate_market_data(num_tokens=5, num_hours=72)  # 3 days
        
        # Get all timestamps
        all_timestamps = set()
        for token_data in market_data.values():
            for data_point in token_data:
                all_timestamps.add(data_point.timestamp)
        
        sorted_timestamps = sorted(all_timestamps)
        logger.info(f"Running simulation over {len(sorted_timestamps)} time steps")
        
        signals_generated = 0
        signals_executed = 0
        
        # Main simulation loop
        for i, timestamp in enumerate(sorted_timestamps):
            # Get market snapshot at this time
            market_snapshot = {}
            for token_address, token_data in market_data.items():
                # Find closest data point
                closest_point = min(token_data, key=lambda x: abs(x.timestamp - timestamp))
                market_snapshot[token_address] = closest_point
            
            # Check stop-loss/take-profit first
            self.check_stop_loss_take_profit(timestamp, market_snapshot)
            
            # Generate and execute signals
            for token_address, data_point in market_snapshot.items():
                signal = self.generate_trading_signal(data_point)
                
                if signal['signal_type'] != 'HOLD':
                    signals_generated += 1
                    
                    # Execute trade
                    old_trade_count = len(self.trades)
                    self.execute_trade(timestamp, token_address, signal, data_point.price)
                    
                    if len(self.trades) > old_trade_count:
                        signals_executed += 1
            
            # Record portfolio value
            total_value = self.current_capital
            for position in self.positions.values():
                total_value += position['entry_value']  # Simplified
            
            self.portfolio_history.append({
                'timestamp': timestamp,
                'total_value': total_value,
                'cash': self.current_capital,
                'positions': len(self.positions)
            })
        
        # Calculate final results
        final_value = self.portfolio_history[-1]['total_value'] if self.portfolio_history else self.current_capital
        total_return = (final_value / self.initial_capital) - 1
        
        # Calculate trade statistics
        completed_trades = [t for t in self.trades if t.action == 'SELL']
        winning_trades = [t for t in completed_trades if t.pnl > 0]
        win_rate = len(winning_trades) / len(completed_trades) if completed_trades else 0
        
        avg_trade_return = np.mean([t.pnl_percent for t in completed_trades]) if completed_trades else 0
        
        results = {
            'initial_capital': self.initial_capital,
            'final_value': final_value,
            'total_return': total_return,
            'total_trades': len(completed_trades),
            'winning_trades': len(winning_trades),
            'win_rate': win_rate,
            'avg_trade_return': avg_trade_return,
            'signals_generated': signals_generated,
            'signals_executed': signals_executed,
            'trades': self.trades,
            'portfolio_history': self.portfolio_history
        }
        
        logger.info("✅ Backtest simulation completed!")
        return results

async def main():
    """Run the demo backtest"""
    
    logger.info("🎯 NewQuantAgent Simple Backtest Demo")
    logger.info("=" * 60)
    logger.info("This demonstrates your actual buy/sell logic with simulated data")
    logger.info("=" * 60)
    
    # Run backtest
    engine = SimpleBacktestEngine(initial_capital=1000.0)
    results = await engine.run_backtest()
    
    # Display results
    print("\n" + "="*60)
    print("📊 BACKTEST RESULTS - YOUR BUY/SELL LOGIC TESTED!")
    print("="*60)
    print(f"💰 Initial Capital:     ${results['initial_capital']:,.2f}")
    print(f"💰 Final Value:         ${results['final_value']:,.2f}")
    print(f"📈 Total Return:        {results['total_return']:.2%}")
    print(f"📊 Total Trades:        {results['total_trades']}")
    print(f"🎯 Win Rate:            {results['win_rate']:.2%}")
    print(f"📊 Avg Trade Return:    {results['avg_trade_return']:.2%}")
    print(f"🔄 Signals Generated:   {results['signals_generated']}")
    print(f"✅ Signals Executed:    {results['signals_executed']}")
    
    # Show sample trades
    if results['trades']:
        print(f"\n📋 Sample Trades:")
        for i, trade in enumerate(results['trades'][:8]):  # Show first 8 trades
            token = trade.token_address[-6:]
            if trade.action == 'BUY':
                print(f"  {i+1}. BUY  {token} at ${trade.price:.6f}")
            else:
                print(f"  {i+1}. SELL {token} at ${trade.price:.6f} ({trade.pnl_percent:+.1f}%)")
    
    print("\n" + "="*60)
    print("🎉 DEMO COMPLETE!")
    print("✅ Your buy/sell logic is working and generating signals!")
    print("📚 This uses the same logic as your live trading system")
    print("🚀 Run 'python run_backtest.py --config standard' for full backtest")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
