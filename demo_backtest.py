#!/usr/bin/env python3
"""
Demo Backtest for NewQuantAgent

Quick demonstration of the backtesting system with a small test.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add NewQuantAgent to path
sys.path.insert(0, str(Path(__file__).parent / "NewQuantAgent"))

from NewQuantAgent.backtesting import BacktestEngine, BacktestConfig, PerformanceAnalyzer

async def run_demo_backtest():
    """Run a quick demo backtest"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    logger = logging.getLogger(__name__)
    
    logger.info("🎯 NewQuantAgent Demo Backtest")
    logger.info("=" * 50)
    
    # Create a small test configuration
    config = BacktestConfig(
        start_date=datetime.now() - timedelta(days=3),  # 3 days
        end_date=datetime.now(),
        initial_capital=1000.0,  # $1,000
        interval_minutes=60,     # 1 hour intervals
        token_count=5,           # 5 tokens
        max_positions=2,         # Max 2 positions
        position_size=0.1,       # 10% per position
        enable_ai_analysis=False, # Disable AI for speed
        enable_social_data=False
    )
    
    logger.info(f"📅 Testing Period: {config.start_date.date()} to {config.end_date.date()}")
    logger.info(f"💰 Initial Capital: ${config.initial_capital:,.2f}")
    logger.info(f"🎯 Tokens to Test: {config.token_count}")
    logger.info(f"📊 Max Positions: {config.max_positions}")
    
    try:
        # Create and run backtest
        logger.info("\n🚀 Starting demo backtest...")
        engine = BacktestEngine(config)
        result = await engine.run_backtest()
        
        if result.success:
            logger.info("✅ Demo backtest completed successfully!")
            
            # Display results
            metrics = result.metrics
            
            print("\n" + "="*60)
            print("📊 DEMO BACKTEST RESULTS")
            print("="*60)
            print(f"💰 Final Portfolio Value: ${metrics.final_value:,.2f}")
            print(f"📈 Total Return: {metrics.total_return:.2%}")
            print(f"📊 Total Trades: {metrics.total_trades}")
            print(f"🎯 Win Rate: {metrics.win_rate:.2%}")
            print(f"📉 Max Drawdown: {metrics.max_drawdown:.2%}")
            print(f"⚡ Sharpe Ratio: {metrics.sharpe_ratio:.3f}")
            print(f"🔄 Signals Generated: {result.signals_generated}")
            print(f"✅ Signals Executed: {result.signals_executed}")
            print(f"⏱️  Execution Time: {result.execution_time:.1f}s")
            
            # Performance rating
            analyzer = PerformanceAnalyzer()
            rating = analyzer._calculate_performance_rating(metrics)
            rating_text = analyzer._get_rating_explanation(rating)
            
            print(f"\n⭐ Performance Rating: {rating}/10")
            print(f"📝 {rating_text}")
            
            print("="*60)
            
            # Show some trade details if any
            if result.trades:
                print(f"\n📋 Sample Trades (showing first 5):")
                for i, trade in enumerate(result.trades[:5]):
                    action = trade['action']
                    token = trade['token_address'][-6:]  # Last 6 chars
                    price = trade['price']
                    
                    if action == 'BUY':
                        print(f"  {i+1}. BUY {token} at ${price:.6f}")
                    else:
                        pnl = trade.get('pnl_percent', 0)
                        print(f"  {i+1}. SELL {token} at ${price:.6f} ({pnl:+.1f}%)")
            
            print(f"\n🎉 Demo completed! The backtesting system is working perfectly.")
            print(f"📚 See BACKTESTING_GUIDE.md for full documentation.")
            print(f"🚀 Run 'python run_backtest.py --config standard' for a full test!")
            
        else:
            logger.error(f"❌ Demo backtest failed: {result.error_message}")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Demo backtest error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(run_demo_backtest())
    sys.exit(exit_code)
