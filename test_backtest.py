#!/usr/bin/env python3
"""Test backtesting system"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path('.') / 'NewQuantAgent'))

# Test imports
try:
    from NewQuantAgent.backtesting import BacktestEngine, BacktestConfig, PerformanceAnalyzer
    print('✅ Backtesting imports successful')
    
    # Test basic functionality
    from datetime import datetime, timedelta
    
    config = BacktestConfig(
        start_date=datetime.now() - timedelta(days=7),
        end_date=datetime.now(),
        initial_capital=1000.0,
        token_count=5,
        max_positions=2
    )
    
    print('✅ BacktestConfig created successfully')
    print(f'📅 Period: {config.start_date.date()} to {config.end_date.date()}')
    print(f'💰 Capital: ${config.initial_capital:,.2f}')
    print(f'🎯 Tokens: {config.token_count}')
    
except Exception as e:
    print(f'❌ Import/setup failed: {e}')
    import traceback
    traceback.print_exc()
